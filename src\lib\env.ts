import { envSchema } from './validation';

// Validate environment variables on startup
function validateEnv() {
  try {
    const env = {
      NODE_ENV: process.env.NODE_ENV,
      DB_HOST: process.env.DB_HOST,
      DB_USER: process.env.DB_USER,
      DB_PASSWORD: process.env.DB_PASSWORD,
      DB_NAME: process.env.DB_NAME,
      DB_PORT: process.env.DB_PORT,
      JWT_SECRET: process.env.JWT_SECRET,
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
      TWILIO_ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID,
      TWILIO_AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN,
      TWILIO_PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER,
      PAYMONGO_SECRET_KEY: process.env.PAYMONGO_SECRET_KEY,
      PAYMONGO_PUBLIC_KEY: process.env.PAYMONGO_PUBLIC_KEY,
      PAYMONGO_WEBHOOK_SECRET: process.env.PAYMONGO_WEBHOOK_SECRET,
    };

    return envSchema.parse(env);
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Environment validation failed:', error);
    }
    throw new Error('Invalid environment configuration');
  }
}

// Export validated environment variables
export const env = validateEnv();

// Helper functions for environment-specific logic
// Environment checks can be done directly with env.NODE_ENV
